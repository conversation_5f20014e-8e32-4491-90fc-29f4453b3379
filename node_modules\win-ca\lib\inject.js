// Generated by LiveScript 1.6.0
var https, tls, der2, toPEM, agentOptions, roots, patchMode, saveCreateSecureContext;
https = require('https');
tls = require('tls');
der2 = require('./der2');
module.exports = iFactory;
iFactory.inject = inject;
toPEM = der2(der2.pem);
agentOptions = https.globalAgent.options;
roots = [];
function iFactory(mode){
  inject(mode, []);
  return add;
}
function add(der){
  roots.push(
  toPEM(
  der));
}
patchMode = 0;
function inject(mode, array){
  if (array) {
    roots.length = 0;
    roots.push.apply(roots, array);
  }
  mode = '+' === mode
    ? 2
    : mode ? 1 : 0;
  if (mode === patchMode) {
    return;
  }
  switch (patchMode) {
  case 1:
    if (agentOptions.ca === roots) {
      delete agentOptions.ca;
    }
    break;
  case 2:
    if (tls.createSecureContext === createSecureContext) {
      tls.createSecureContext = saveCreateSecureContext;
      saveCreateSecureContext = void 8;
    }
  }
  switch (patchMode = mode) {
  case 1:
    agentOptions.ca = roots;
    break;
  case 2:
    if (!saveCreateSecureContext) {
      saveCreateSecureContext = tls.createSecureContext;
      tls.createSecureContext = createSecureContext;
    }
  }
}
function createSecureContext(options){
  var ctx, i$, ref$, len$, crt;
  ctx = saveCreateSecureContext.apply(this, arguments);
  if (2 === patchMode && !(options != null && options.ca)) {
    for (i$ = 0, len$ = (ref$ = roots).length; i$ < len$; ++i$) {
      crt = ref$[i$];
      ctx.context.addCACert(crt);
    }
  }
  return ctx;
}