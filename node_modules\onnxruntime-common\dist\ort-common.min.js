/*!
 * ONNX Runtime Common v1.14.0
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.ort=e():t.ort=e()}(self,(()=>(()=>{"use strict";var t={d:(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{InferenceSession:()=>c,Tensor:()=>g,env:()=>i,registerBackend:()=>o});const r={},n=[],o=(t,e,o)=>{if(!e||"function"!=typeof e.init||"function"!=typeof e.createSessionHandler)throw new TypeError("not a valid backend");{const i=r[t];if(void 0===i)r[t]={backend:e,priority:o};else{if(i.priority>o)return;if(i.priority===o&&i.backend!==e)throw new Error(`cannot register backend "${t}" using priority ${o}`)}if(o>=0){const e=n.indexOf(t);-1!==e&&n.splice(e,1);for(let e=0;e<n.length;e++)if(r[n[e]].priority<=o)return void n.splice(e,0,t);n.push(t)}}},i=new class{constructor(){this.wasm={},this.webgl={},this.logLevelInternal="warning"}set logLevel(t){if(void 0!==t){if("string"!=typeof t||-1===["verbose","info","warning","error","fatal"].indexOf(t))throw new Error(`Unsupported logging level: ${t}`);this.logLevelInternal=t}}get logLevel(){return this.logLevelInternal}},a="undefined"!=typeof BigInt64Array&&"function"==typeof BigInt64Array.from,s="undefined"!=typeof BigUint64Array&&"function"==typeof BigUint64Array.from,f=new Map([["float32",Float32Array],["uint8",Uint8Array],["int8",Int8Array],["uint16",Uint16Array],["int16",Int16Array],["int32",Int32Array],["bool",Uint8Array],["float64",Float64Array],["uint32",Uint32Array]]),d=new Map([[Float32Array,"float32"],[Uint8Array,"uint8"],[Int8Array,"int8"],[Uint16Array,"uint16"],[Int16Array,"int16"],[Int32Array,"int32"],[Float64Array,"float64"],[Uint32Array,"uint32"]]);a&&(f.set("int64",BigInt64Array),d.set(BigInt64Array,"int64")),s&&(f.set("uint64",BigUint64Array),d.set(BigUint64Array,"uint64"));class h{constructor(t,e,r){let n,o,i;if("string"==typeof t)if(n=t,i=r,"string"===t){if(!Array.isArray(e))throw new TypeError("A string tensor's data must be a string array.");o=e}else{const r=f.get(t);if(void 0===r)throw new TypeError(`Unsupported tensor type: ${t}.`);if(Array.isArray(e))o=r.from(e);else{if(!(e instanceof r))throw new TypeError(`A ${n} tensor's data must be type of ${r}`);o=e}}else if(i=e,Array.isArray(t)){if(0===t.length)throw new TypeError("Tensor type cannot be inferred from an empty array.");const e=typeof t[0];if("string"===e)n="string",o=t;else{if("boolean"!==e)throw new TypeError(`Invalid element type of data array: ${e}.`);n="bool",o=Uint8Array.from(t)}}else{const e=d.get(t.constructor);if(void 0===e)throw new TypeError(`Unsupported type for tensor data: ${t.constructor}.`);n=e,o=t}if(void 0===i)i=[o.length];else if(!Array.isArray(i))throw new TypeError("A tensor's dims must be a number array");const a=(t=>{let e=1;for(let r=0;r<t.length;r++){const n=t[r];if("number"!=typeof n||!Number.isSafeInteger(n))throw new TypeError(`dims[${r}] must be an integer, got: ${n}`);if(n<0)throw new RangeError(`dims[${r}] must be a non-negative integer, got: ${n}`);e*=n}return e})(i);if(a!==o.length)throw new Error(`Tensor's size(${a}) does not match data length(${o.length}).`);this.dims=i,this.type=n,this.data=o,this.size=a}static bufferToTensor(t,e){if(void 0===t)throw new Error("Image buffer must be defined");if(void 0===e.height||void 0===e.width)throw new Error("Image height and width must be defined");const{height:r,width:n}=e,o=e.norm;let i,a;i=void 0===o||void 0===o.mean?255:o.mean,a=void 0===o||void 0===o.bias?0:o.bias;const s=void 0!==e.bitmapFormat?e.bitmapFormat:"RGBA",f=void 0!==e.tensorFormat&&void 0!==e.tensorFormat?e.tensorFormat:"RGB",d=r*n,g="RGBA"===f?new Float32Array(4*d):new Float32Array(3*d);let m=4,c=0,l=1,u=2,w=3,p=0,y=d,b=2*d,v=-1;"RGB"===s&&(m=3,c=0,l=1,u=2,w=-1),"RGBA"===f?v=3*d:"RBG"===f?(p=0,b=d,y=2*d):"BGR"===f&&(b=0,y=d,p=2*d);for(let e=0;e<d;e++,c+=m,u+=m,l+=m,w+=m)g[p++]=(t[c]+a)/i,g[y++]=(t[l]+a)/i,g[b++]=(t[u]+a)/i,-1!==v&&-1!==w&&(g[v++]=(t[w]+a)/i);return new h("float32",g,"RGBA"===f?[1,4,r,n]:[1,3,r,n])}static async fromImage(t,e){const r="undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement,n="undefined"!=typeof ImageData&&t instanceof ImageData,o="undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap,i="undefined"!=typeof String&&(t instanceof String||"string"==typeof t);let a,s={};if(r){const r=document.createElement("canvas"),n=r.getContext("2d");if(null==n)throw new Error("Can not access image data");{let o=t.naturalHeight,i=t.naturalWidth;if(void 0!==e&&void 0!==e.resizedHeight&&void 0!==e.resizedWidth&&(o=e.resizedHeight,i=e.resizedWidth),void 0!==e){if(s=e,void 0!==e.tensorFormat)throw new Error("Image input config format must be RGBA for HTMLImageElement");if(s.tensorFormat="RGBA",void 0!==e.height&&e.height!==o)throw new Error("Image input config height doesn't match HTMLImageElement height");if(s.height=o,void 0!==e.width&&e.width!==i)throw new Error("Image input config width doesn't match HTMLImageElement width");s.width=i}else s.tensorFormat="RGBA",s.height=o,s.width=i;r.width=i,r.height=o,n.drawImage(t,0,0,i,o),a=n.getImageData(0,0,i,o).data}}else{if(!n){if(o){if(void 0===e)throw new Error("Please provide image config with format for Imagebitmap");if(void 0!==e.bitmapFormat)throw new Error("Image input config format must be defined for ImageBitmap");const r=document.createElement("canvas").getContext("2d");if(null!=r){const n=t.height,o=t.width;if(r.drawImage(t,0,0,o,n),a=r.getImageData(0,0,o,n).data,void 0!==e){if(void 0!==e.height&&e.height!==n)throw new Error("Image input config height doesn't match ImageBitmap height");if(s.height=n,void 0!==e.width&&e.width!==o)throw new Error("Image input config width doesn't match ImageBitmap width");s.width=o}else s.height=n,s.width=o;return h.bufferToTensor(a,s)}throw new Error("Can not access image data")}if(i)return new Promise(((r,n)=>{const o=document.createElement("canvas"),i=o.getContext("2d");if(!t||!i)return n();const a=new Image;a.crossOrigin="Anonymous",a.src=t,a.onload=()=>{o.width=a.width,o.height=a.height,i.drawImage(a,0,0,o.width,o.height);const t=i.getImageData(0,0,o.width,o.height);if(void 0!==e){if(void 0!==e.height&&e.height!==o.height)throw new Error("Image input config height doesn't match ImageBitmap height");if(s.height=o.height,void 0!==e.width&&e.width!==o.width)throw new Error("Image input config width doesn't match ImageBitmap width");s.width=o.width}else s.height=o.height,s.width=o.width;r(h.bufferToTensor(t.data,s))}}));throw new Error("Input data provided is not supported - aborted tensor creation")}{const r="RGBA";let n,o;if(void 0!==e&&void 0!==e.resizedWidth&&void 0!==e.resizedHeight?(n=e.resizedHeight,o=e.resizedWidth):(n=t.height,o=t.width),void 0!==e){if(s=e,void 0!==e.bitmapFormat&&e.bitmapFormat!==r)throw new Error("Image input config format must be RGBA for ImageData");s.bitmapFormat="RGBA"}else s.bitmapFormat="RGBA";if(s.height=n,s.width=o,void 0!==e){const e=document.createElement("canvas");e.width=o,e.height=n;const r=e.getContext("2d");if(null==r)throw new Error("Can not access image data");r.putImageData(t,0,0),a=r.getImageData(0,0,o,n).data}else a=t.data}}if(void 0!==a)return h.bufferToTensor(a,s);throw new Error("Input data provided is not supported - aborted tensor creation")}toImageData(t){var e,r;const n=document.createElement("canvas").getContext("2d");let o;if(null==n)throw new Error("Can not access image data");{const i=this.dims[3],a=this.dims[2],s=this.dims[1],f=void 0!==t&&void 0!==t.format?t.format:"RGB",d=void 0!==t&&void 0!==(null===(e=t.norm)||void 0===e?void 0:e.mean)?t.norm.mean:255,h=void 0!==t&&void 0!==(null===(r=t.norm)||void 0===r?void 0:r.bias)?t.norm.bias:0,g=a*i;if(void 0!==t){if(void 0!==t.height&&t.height!==a)throw new Error("Image output config height doesn't match tensor height");if(void 0!==t.width&&t.width!==i)throw new Error("Image output config width doesn't match tensor width");if(void 0!==t.format&&4===s&&"RGBA"!==t.format||3===s&&"RGB"!==t.format&&"BGR"!==t.format)throw new Error("Tensor format doesn't match input tensor dims")}const m=4;let c=0,l=1,u=2,w=3,p=0,y=g,b=2*g,v=-1;"RGBA"===f?(p=0,y=g,b=2*g,v=3*g):"RGB"===f?(p=0,y=g,b=2*g):"RBG"===f&&(p=0,b=g,y=2*g),o=n.createImageData(i,a);for(let t=0;t<a*i;c+=m,l+=m,u+=m,w+=m,t++)o.data[c]=(this.data[p++]-h)*d,o.data[l]=(this.data[y++]-h)*d,o.data[u]=(this.data[b++]-h)*d,o.data[w]=-1===v?255:(this.data[v++]-h)*d}return o}reshape(t){return new h(this.type,this.data,t)}}const g=h;class m{constructor(t){this.handler=t}async run(t,e,r){const n={};let o={};if("object"!=typeof t||null===t||t instanceof g||Array.isArray(t))throw new TypeError("'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.");let i=!0;if("object"==typeof e){if(null===e)throw new TypeError("Unexpected argument[1]: cannot be null.");if(e instanceof g)throw new TypeError("'fetches' cannot be a Tensor");if(Array.isArray(e)){if(0===e.length)throw new TypeError("'fetches' cannot be an empty array.");i=!1;for(const t of e){if("string"!=typeof t)throw new TypeError("'fetches' must be a string array or an object.");if(-1===this.outputNames.indexOf(t))throw new RangeError(`'fetches' contains invalid output name: ${t}.`);n[t]=null}if("object"==typeof r&&null!==r)o=r;else if(void 0!==r)throw new TypeError("'options' must be an object.")}else{let t=!1;const a=Object.getOwnPropertyNames(e);for(const r of this.outputNames)if(-1!==a.indexOf(r)){const o=e[r];(null===o||o instanceof g)&&(t=!0,i=!1,n[r]=o)}if(t){if("object"==typeof r&&null!==r)o=r;else if(void 0!==r)throw new TypeError("'options' must be an object.")}else o=e}}else if(void 0!==e)throw new TypeError("Unexpected argument[1]: must be 'fetches' or 'options'.");for(const e of this.inputNames)if(void 0===t[e])throw new Error(`input '${e}' is missing in 'feeds'.`);if(i)for(const t of this.outputNames)n[t]=null;const a=await this.handler.run(t,n,o),s={};for(const t in a)Object.hasOwnProperty.call(a,t)&&(s[t]=new g(a[t].type,a[t].data,a[t].dims));return s}static async create(t,e,o,i){let a,s={};if("string"==typeof t){if(a=t,"object"==typeof e&&null!==e)s=e;else if(void 0!==e)throw new TypeError("'options' must be an object.")}else if(t instanceof Uint8Array){if(a=t,"object"==typeof e&&null!==e)s=e;else if(void 0!==e)throw new TypeError("'options' must be an object.")}else{if(!(t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer))throw new TypeError("Unexpected argument[0]: must be 'path' or 'buffer'.");{const r=t;let n=0,f=t.byteLength;if("object"==typeof e&&null!==e)s=e;else if("number"==typeof e){if(n=e,!Number.isSafeInteger(n))throw new RangeError("'byteOffset' must be an integer.");if(n<0||n>=r.byteLength)throw new RangeError(`'byteOffset' is out of range [0, ${r.byteLength}).`);if(f=t.byteLength-n,"number"==typeof o){if(f=o,!Number.isSafeInteger(f))throw new RangeError("'byteLength' must be an integer.");if(f<=0||n+f>r.byteLength)throw new RangeError(`'byteLength' is out of range (0, ${r.byteLength-n}].`);if("object"==typeof i&&null!==i)s=i;else if(void 0!==i)throw new TypeError("'options' must be an object.")}else if(void 0!==o)throw new TypeError("'byteLength' must be a number.")}else if(void 0!==e)throw new TypeError("'options' must be an object.");a=new Uint8Array(r,n,f)}}const f=(s.executionProviders||[]).map((t=>"string"==typeof t?t:t.name)),d=await(async t=>{const e=0===t.length?n:t,o=[];for(const t of e){const e=r[t];if(e){if(e.initialized)return e.backend;if(e.aborted)continue;const r=!!e.initPromise;try{return r||(e.initPromise=e.backend.init()),await e.initPromise,e.initialized=!0,e.backend}catch(n){r||o.push({name:t,err:n}),e.aborted=!0}finally{delete e.initPromise}}}throw new Error(`no available backend found. ERR: ${o.map((t=>`[${t.name}] ${t.err}`)).join(", ")}`)})(f),h=await d.createSessionHandler(a,s);return new m(h)}startProfiling(){this.handler.startProfiling()}endProfiling(){this.handler.endProfiling()}get inputNames(){return this.handler.inputNames}get outputNames(){return this.handler.outputNames}}const c=m;return e})()));
//# sourceMappingURL=ort-common.min.js.map