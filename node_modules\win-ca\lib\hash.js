// Generated by LiveScript 1.6.0
var crypto, forge, der2, asn1, toASN1, bufferFrom;
crypto = require('crypto');
forge = require('./forge');
der2 = require('./der2');
asn1 = forge().asn1;
toASN1 = der2(der2.asn1);
module.exports = dispatch;
bufferFrom = Buffer.from || function(data, encoding){
  return new Buffer(data, encoding);
};
function hash(it){
  var sha1;
  sha1 = crypto.createHash('sha1');
  toASN1(it).subject.value.forEach(function(it){
    var pair, unicod;
    it = asn1.copy(it);
    pair = it.value[0].value[1];
    if (!pair.value) {
      return;
    }
    pair.type = asn1.Type.UTF8;
    unicod = bufferFrom(pair.value, 'binary').toString('utf8');
    unicod = unicod.trim().replace(/[A-Z]+/g, function(it){
      return it.toLowerCase();
    }).replace(/\s+/g, ' ');
    pair.value = bufferFrom(unicod, 'utf8').toString('binary');
    sha1.update(asn1.toDer(it).getBytes(), 'binary');
  });
  return hex(sha1);
}
function hash0(it){
  var md5, subj;
  md5 = crypto.createHash('md5');
  subj = toASN1(it).subject;
  md5.update(asn1.toDer(subj).getBytes(), 'binary');
  return hex(md5);
}
function hex(it){
  it = it.digest().slice(0, 4);
  it.writeUInt32LE(it.readUInt32BE(0), 0);
  return it.toString('hex');
}
function dispatch(ver, blob){
  var fn;
  fn = 0 === ver ? hash0 : hash;
  if (blob != null) {
    return fn(blob);
  } else {
    return fn;
  }
}