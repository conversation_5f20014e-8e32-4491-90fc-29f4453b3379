// Generated by LiveScript 1.6.0
var crypt32, out$ = typeof exports != 'undefined' && exports || this;
crypt32 = require("./crypt32-" + process.arch);
out$.sync = sync;
function sync(args){
  var handle, current;
  current = args.length
    ? 0
    : -1;
  return {
    next: next,
    done: done,
    run: run
  };
  function next(){
    var that;
    while (current < args.length) {
      handle == null && (handle = current < 0
        ? crypt32()
        : crypt32(args[current]));
      if (that = handle.next()) {
        return that;
      }
      handle.done();
      handle = void 8;
      current++;
    }
  }
  function done(){
    if (handle != null) {
      handle.done();
    }
    handle = void 8;
    current = args.length;
  }
  function run(it){
    var that;
    while (that = next()) {
      it(that);
    }
    it();
  }
}
out$.async = async;
function async(it){
  var res, next;
  res = sync(it);
  next = res.next;
  res.run = run;
  return res;
  function run(callback){
    function fire(){
      return Promise.resolve().then(next).then(function(it){
        if (it) {
          callback(it);
          return fire();
        }
        callback();
      });
    } fire();
  }
}