/*!
 * ONNX Runtime Common v1.14.0
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ort=t():e.ort=t()}(self,(function(){return function(){"use strict";var e={d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{InferenceSession:function(){return p},Tensor:function(){return h},env:function(){return a},registerBackend:function(){return o}});var r={},n=[],o=function(e,t,o){if(!t||"function"!=typeof t.init||"function"!=typeof t.createSessionHandler)throw new TypeError("not a valid backend");var i=r[e];if(void 0===i)r[e]={backend:t,priority:o};else{if(i.priority>o)return;if(i.priority===o&&i.backend!==t)throw new Error('cannot register backend "'.concat(e,'" using priority ').concat(o))}if(o>=0){var a=n.indexOf(e);-1!==a&&n.splice(a,1);for(var f=0;f<n.length;f++)if(r[n[f]].priority<=o)return void n.splice(f,0,e);n.push(e)}},i=function(e){return t=void 0,o=void 0,a=function(){var t,o,i,a,f,s,u,c;return function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:f(0),throw:f(1),return:f(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function f(f){return function(s){return function(f){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,f[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&f[0]?n.return:f[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,f[1])).done)return o;switch(n=0,o&&(f=[2&f[0],o.value]),f[0]){case 0:case 1:o=f;break;case 4:return a.label++,{value:f[1],done:!1};case 5:a.label++,n=f[1],f=[0];continue;case 7:f=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==f[0]&&2!==f[0])){a=0;continue}if(3===f[0]&&(!o||f[1]>o[0]&&f[1]<o[3])){a.label=f[1];break}if(6===f[0]&&a.label<o[1]){a.label=o[1],o=f;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(f);break}o[2]&&a.ops.pop(),a.trys.pop();continue}f=t.call(e,a)}catch(e){f=[6,e],n=0}finally{r=o=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}([f,s])}}}(this,(function(h){switch(h.label){case 0:t=0===e.length?n:e,o=[],i=0,a=t,h.label=1;case 1:if(!(i<a.length))return[3,7];if(f=a[i],!(s=r[f]))return[3,6];if(s.initialized)return[2,s.backend];if(s.aborted)return[3,6];u=!!s.initPromise,h.label=2;case 2:return h.trys.push([2,4,5,6]),u||(s.initPromise=s.backend.init()),[4,s.initPromise];case 3:return h.sent(),s.initialized=!0,[2,s.backend];case 4:return c=h.sent(),u||o.push({name:f,err:c}),s.aborted=!0,[3,6];case 5:return delete s.initPromise,[7];case 6:return i++,[3,1];case 7:throw new Error("no available backend found. ERR: ".concat(o.map((function(e){return"[".concat(e.name,"] ").concat(e.err)})).join(", ")))}}))},new((i=void 0)||(i=Promise))((function(e,r){function n(e){try{s(a.next(e))}catch(e){r(e)}}function f(e){try{s(a.throw(e))}catch(e){r(e)}}function s(t){var r;t.done?e(t.value):(r=t.value,r instanceof i?r:new i((function(e){e(r)}))).then(n,f)}s((a=a.apply(t,o||[])).next())}));var t,o,i,a},a=new(function(){function e(){this.wasm={},this.webgl={},this.logLevelInternal="warning"}return Object.defineProperty(e.prototype,"logLevel",{get:function(){return this.logLevelInternal},set:function(e){if(void 0!==e){if("string"!=typeof e||-1===["verbose","info","warning","error","fatal"].indexOf(e))throw new Error("Unsupported logging level: ".concat(e));this.logLevelInternal=e}},enumerable:!1,configurable:!0}),e}()),f="undefined"!=typeof BigInt64Array&&"function"==typeof BigInt64Array.from,s="undefined"!=typeof BigUint64Array&&"function"==typeof BigUint64Array.from,u=new Map([["float32",Float32Array],["uint8",Uint8Array],["int8",Int8Array],["uint16",Uint16Array],["int16",Int16Array],["int32",Int32Array],["bool",Uint8Array],["float64",Float64Array],["uint32",Uint32Array]]),c=new Map([[Float32Array,"float32"],[Uint8Array,"uint8"],[Int8Array,"int8"],[Uint16Array,"uint16"],[Int16Array,"int16"],[Int32Array,"int32"],[Float64Array,"float64"],[Uint32Array,"uint32"]]);f&&(u.set("int64",BigInt64Array),c.set(BigInt64Array,"int64")),s&&(u.set("uint64",BigUint64Array),c.set(BigUint64Array,"uint64"));var h=function(){function e(e,t,r){var n,o,i;if("string"==typeof e)if(n=e,i=r,"string"===e){if(!Array.isArray(t))throw new TypeError("A string tensor's data must be a string array.");o=t}else{var a=u.get(e);if(void 0===a)throw new TypeError("Unsupported tensor type: ".concat(e,"."));if(Array.isArray(t))o=a.from(t);else{if(!(t instanceof a))throw new TypeError("A ".concat(n," tensor's data must be type of ").concat(a));o=t}}else if(i=t,Array.isArray(e)){if(0===e.length)throw new TypeError("Tensor type cannot be inferred from an empty array.");var f=typeof e[0];if("string"===f)n="string",o=e;else{if("boolean"!==f)throw new TypeError("Invalid element type of data array: ".concat(f,"."));n="bool",o=Uint8Array.from(e)}}else{var s=c.get(e.constructor);if(void 0===s)throw new TypeError("Unsupported type for tensor data: ".concat(e.constructor,"."));n=s,o=e}if(void 0===i)i=[o.length];else if(!Array.isArray(i))throw new TypeError("A tensor's dims must be a number array");var h=function(e){for(var t=1,r=0;r<e.length;r++){var n=e[r];if("number"!=typeof n||!Number.isSafeInteger(n))throw new TypeError("dims[".concat(r,"] must be an integer, got: ").concat(n));if(n<0)throw new RangeError("dims[".concat(r,"] must be a non-negative integer, got: ").concat(n));t*=n}return t}(i);if(h!==o.length)throw new Error("Tensor's size(".concat(h,") does not match data length(").concat(o.length,")."));this.dims=i,this.type=n,this.data=o,this.size=h}return e.bufferToTensor=function(t,r){if(void 0===t)throw new Error("Image buffer must be defined");if(void 0===r.height||void 0===r.width)throw new Error("Image height and width must be defined");var n,o,i=r.height,a=r.width,f=r.norm;n=void 0===f||void 0===f.mean?255:f.mean,o=void 0===f||void 0===f.bias?0:f.bias;var s=void 0!==r.bitmapFormat?r.bitmapFormat:"RGBA",u=void 0!==r.tensorFormat&&void 0!==r.tensorFormat?r.tensorFormat:"RGB",c=i*a,h="RGBA"===u?new Float32Array(4*c):new Float32Array(3*c),d=4,l=0,p=1,g=2,m=3,w=0,y=c,b=2*c,v=-1;"RGB"===s&&(d=3,l=0,p=1,g=2,m=-1),"RGBA"===u?v=3*c:"RBG"===u?(w=0,b=c,y=2*c):"BGR"===u&&(b=0,y=c,w=2*c);for(var E=0;E<c;E++,l+=d,g+=d,p+=d,m+=d)h[w++]=(t[l]+o)/n,h[y++]=(t[p]+o)/n,h[b++]=(t[g]+o)/n,-1!==v&&-1!==m&&(h[v++]=(t[m]+o)/n);return new e("float32",h,"RGBA"===u?[1,4,i,a]:[1,3,i,a])},e.fromImage=function(t,r){return n=this,o=void 0,a=function(){var n,o,i,a,f,s,u,c,h,d,l;return function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:f(0),throw:f(1),return:f(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function f(f){return function(s){return function(f){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,f[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&f[0]?n.return:f[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,f[1])).done)return o;switch(n=0,o&&(f=[2&f[0],o.value]),f[0]){case 0:case 1:o=f;break;case 4:return a.label++,{value:f[1],done:!1};case 5:a.label++,n=f[1],f=[0];continue;case 7:f=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==f[0]&&2!==f[0])){a=0;continue}if(3===f[0]&&(!o||f[1]>o[0]&&f[1]<o[3])){a.label=f[1];break}if(6===f[0]&&a.label<o[1]){a.label=o[1],o=f;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(f);break}o[2]&&a.ops.pop(),a.trys.pop();continue}f=t.call(e,a)}catch(e){f=[6,e],n=0}finally{r=o=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}([f,s])}}}(this,(function(p){if(n="undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement,o="undefined"!=typeof ImageData&&t instanceof ImageData,i="undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap,a="undefined"!=typeof String&&(t instanceof String||"string"==typeof t),s={},n){if(u=document.createElement("canvas"),null==(h=u.getContext("2d")))throw new Error("Can not access image data");if(d=t.naturalHeight,l=t.naturalWidth,void 0!==r&&void 0!==r.resizedHeight&&void 0!==r.resizedWidth&&(d=r.resizedHeight,l=r.resizedWidth),void 0!==r){if(s=r,void 0!==r.tensorFormat)throw new Error("Image input config format must be RGBA for HTMLImageElement");if(s.tensorFormat="RGBA",void 0!==r.height&&r.height!==d)throw new Error("Image input config height doesn't match HTMLImageElement height");if(s.height=d,void 0!==r.width&&r.width!==l)throw new Error("Image input config width doesn't match HTMLImageElement width");s.width=l}else s.tensorFormat="RGBA",s.height=d,s.width=l;u.width=l,u.height=d,h.drawImage(t,0,0,l,d),f=h.getImageData(0,0,l,d).data}else{if(!o){if(i){if(void 0===r)throw new Error("Please provide image config with format for Imagebitmap");if(void 0!==r.bitmapFormat)throw new Error("Image input config format must be defined for ImageBitmap");if(null!=(h=document.createElement("canvas").getContext("2d"))){if(d=t.height,l=t.width,h.drawImage(t,0,0,l,d),f=h.getImageData(0,0,l,d).data,void 0!==r){if(void 0!==r.height&&r.height!==d)throw new Error("Image input config height doesn't match ImageBitmap height");if(s.height=d,void 0!==r.width&&r.width!==l)throw new Error("Image input config width doesn't match ImageBitmap width");s.width=l}else s.height=d,s.width=l;return[2,e.bufferToTensor(f,s)]}throw new Error("Can not access image data")}if(a)return[2,new Promise((function(n,o){var i=document.createElement("canvas"),a=i.getContext("2d");if(!t||!a)return o();var f=new Image;f.crossOrigin="Anonymous",f.src=t,f.onload=function(){i.width=f.width,i.height=f.height,a.drawImage(f,0,0,i.width,i.height);var t=a.getImageData(0,0,i.width,i.height);if(void 0!==r){if(void 0!==r.height&&r.height!==i.height)throw new Error("Image input config height doesn't match ImageBitmap height");if(s.height=i.height,void 0!==r.width&&r.width!==i.width)throw new Error("Image input config width doesn't match ImageBitmap width");s.width=i.width}else s.height=i.height,s.width=i.width;n(e.bufferToTensor(t.data,s))}}))];throw new Error("Input data provided is not supported - aborted tensor creation")}if(d=void 0,l=void 0,void 0!==r&&void 0!==r.resizedWidth&&void 0!==r.resizedHeight?(d=r.resizedHeight,l=r.resizedWidth):(d=t.height,l=t.width),void 0!==r){if(s=r,void 0!==r.bitmapFormat&&"RGBA"!==r.bitmapFormat)throw new Error("Image input config format must be RGBA for ImageData");s.bitmapFormat="RGBA"}else s.bitmapFormat="RGBA";if(s.height=d,s.width=l,void 0!==r){if((c=document.createElement("canvas")).width=l,c.height=d,null==(h=c.getContext("2d")))throw new Error("Can not access image data");h.putImageData(t,0,0),f=h.getImageData(0,0,l,d).data}else f=t.data}if(void 0!==f)return[2,e.bufferToTensor(f,s)];throw new Error("Input data provided is not supported - aborted tensor creation")}))},new((i=void 0)||(i=Promise))((function(e,t){function r(e){try{s(a.next(e))}catch(e){t(e)}}function f(e){try{s(a.throw(e))}catch(e){t(e)}}function s(t){var n;t.done?e(t.value):(n=t.value,n instanceof i?n:new i((function(e){e(n)}))).then(r,f)}s((a=a.apply(n,o||[])).next())}));var n,o,i,a},e.prototype.toImageData=function(e){var t,r,n,o=document.createElement("canvas").getContext("2d");if(null==o)throw new Error("Can not access image data");var i=this.dims[3],a=this.dims[2],f=this.dims[1],s=void 0!==e&&void 0!==e.format?e.format:"RGB",u=void 0!==e&&void 0!==(null===(t=e.norm)||void 0===t?void 0:t.mean)?e.norm.mean:255,c=void 0!==e&&void 0!==(null===(r=e.norm)||void 0===r?void 0:r.bias)?e.norm.bias:0,h=a*i;if(void 0!==e){if(void 0!==e.height&&e.height!==a)throw new Error("Image output config height doesn't match tensor height");if(void 0!==e.width&&e.width!==i)throw new Error("Image output config width doesn't match tensor width");if(void 0!==e.format&&4===f&&"RGBA"!==e.format||3===f&&"RGB"!==e.format&&"BGR"!==e.format)throw new Error("Tensor format doesn't match input tensor dims")}var d=0,l=1,p=2,g=3,m=0,w=h,y=2*h,b=-1;"RGBA"===s?(m=0,w=h,y=2*h,b=3*h):"RGB"===s?(m=0,w=h,y=2*h):"RBG"===s&&(m=0,y=h,w=2*h),n=o.createImageData(i,a);for(var v=0;v<a*i;d+=4,l+=4,p+=4,g+=4,v++)n.data[d]=(this.data[m++]-c)*u,n.data[l]=(this.data[w++]-c)*u,n.data[p]=(this.data[y++]-c)*u,n.data[g]=-1===b?255:(this.data[b++]-c)*u;return n},e.prototype.reshape=function(t){return new e(this.type,this.data,t)},e}(),d=function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{s(n.next(e))}catch(e){i(e)}}function f(e){try{s(n.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,f)}s((n=n.apply(e,t||[])).next())}))},l=function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:f(0),throw:f(1),return:f(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function f(f){return function(s){return function(f){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,f[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&f[0]?n.return:f[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,f[1])).done)return o;switch(n=0,o&&(f=[2&f[0],o.value]),f[0]){case 0:case 1:o=f;break;case 4:return a.label++,{value:f[1],done:!1};case 5:a.label++,n=f[1],f=[0];continue;case 7:f=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==f[0]&&2!==f[0])){a=0;continue}if(3===f[0]&&(!o||f[1]>o[0]&&f[1]<o[3])){a.label=f[1];break}if(6===f[0]&&a.label<o[1]){a.label=o[1],o=f;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(f);break}o[2]&&a.ops.pop(),a.trys.pop();continue}f=t.call(e,a)}catch(e){f=[6,e],n=0}finally{r=o=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}([f,s])}}},p=function(){function e(e){this.handler=e}return e.prototype.run=function(e,t,r){return d(this,void 0,void 0,(function(){var n,o,i,a,f,s,u,c,d,p,g,m,w,y,b,v,E,A,I,T,B;return l(this,(function(l){switch(l.label){case 0:if(n={},o={},"object"!=typeof e||null===e||e instanceof h||Array.isArray(e))throw new TypeError("'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.");if(i=!0,"object"==typeof t){if(null===t)throw new TypeError("Unexpected argument[1]: cannot be null.");if(t instanceof h)throw new TypeError("'fetches' cannot be a Tensor");if(Array.isArray(t)){if(0===t.length)throw new TypeError("'fetches' cannot be an empty array.");for(i=!1,a=0,f=t;a<f.length;a++){if("string"!=typeof(s=f[a]))throw new TypeError("'fetches' must be a string array or an object.");if(-1===this.outputNames.indexOf(s))throw new RangeError("'fetches' contains invalid output name: ".concat(s,"."));n[s]=null}if("object"==typeof r&&null!==r)o=r;else if(void 0!==r)throw new TypeError("'options' must be an object.")}else{for(u=!1,c=Object.getOwnPropertyNames(t),d=0,p=this.outputNames;d<p.length;d++)g=p[d],-1!==c.indexOf(g)&&(null===(m=t[g])||m instanceof h)&&(u=!0,i=!1,n[g]=m);if(u){if("object"==typeof r&&null!==r)o=r;else if(void 0!==r)throw new TypeError("'options' must be an object.")}else o=t}}else if(void 0!==t)throw new TypeError("Unexpected argument[1]: must be 'fetches' or 'options'.");for(w=0,y=this.inputNames;w<y.length;w++)if(b=y[w],void 0===e[b])throw new Error("input '".concat(b,"' is missing in 'feeds'."));if(i)for(v=0,E=this.outputNames;v<E.length;v++)A=E[v],n[A]=null;return[4,this.handler.run(e,n,o)];case 1:for(B in I=l.sent(),T={},I)Object.hasOwnProperty.call(I,B)&&(T[B]=new h(I[B].type,I[B].data,I[B].dims));return[2,T]}}))}))},e.create=function(t,r,n,o){return d(this,void 0,void 0,(function(){var a,f,s,u,c,h,d;return l(this,(function(l){switch(l.label){case 0:if(f={},"string"==typeof t){if(a=t,"object"==typeof r&&null!==r)f=r;else if(void 0!==r)throw new TypeError("'options' must be an object.")}else if(t instanceof Uint8Array){if(a=t,"object"==typeof r&&null!==r)f=r;else if(void 0!==r)throw new TypeError("'options' must be an object.")}else{if(!(t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer))throw new TypeError("Unexpected argument[0]: must be 'path' or 'buffer'.");if(s=t,u=0,c=t.byteLength,"object"==typeof r&&null!==r)f=r;else if("number"==typeof r){if(u=r,!Number.isSafeInteger(u))throw new RangeError("'byteOffset' must be an integer.");if(u<0||u>=s.byteLength)throw new RangeError("'byteOffset' is out of range [0, ".concat(s.byteLength,")."));if(c=t.byteLength-u,"number"==typeof n){if(c=n,!Number.isSafeInteger(c))throw new RangeError("'byteLength' must be an integer.");if(c<=0||u+c>s.byteLength)throw new RangeError("'byteLength' is out of range (0, ".concat(s.byteLength-u,"]."));if("object"==typeof o&&null!==o)f=o;else if(void 0!==o)throw new TypeError("'options' must be an object.")}else if(void 0!==n)throw new TypeError("'byteLength' must be a number.")}else if(void 0!==r)throw new TypeError("'options' must be an object.");a=new Uint8Array(s,u,c)}return h=f.executionProviders||[],d=h.map((function(e){return"string"==typeof e?e:e.name})),[4,i(d)];case 1:return[4,l.sent().createSessionHandler(a,f)];case 2:return[2,new e(l.sent())]}}))}))},e.prototype.startProfiling=function(){this.handler.startProfiling()},e.prototype.endProfiling=function(){this.handler.endProfiling()},Object.defineProperty(e.prototype,"inputNames",{get:function(){return this.handler.inputNames},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"outputNames",{get:function(){return this.handler.outputNames},enumerable:!1,configurable:!0}),e}();return t}()}));
//# sourceMappingURL=ort-common.es5.min.js.map