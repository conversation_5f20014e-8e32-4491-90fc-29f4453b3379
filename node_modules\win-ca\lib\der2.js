// Generated by LiveScript 1.6.0
var forge$, formatters, list, k, v, isBuffer, bufferFrom;
module.exports = der2;
forge$ = require('./forge');
formatters = {
  der: der,
  pem: pem,
  txt: txt,
  asn1: asn1,
  x509: x509
};
list = [];
for (k in formatters) {
  v = formatters[k];
  der2[k] = list.length;
  list.push(v);
}
der2.forge = der2.x509;
isBuffer = Buffer.isBuffer;
bufferFrom = Buffer.from || function(data, encoding){
  return new Buffer(data, encoding);
};
function der2(format, blob){
  var converter;
  converter = list[format] || list[0];
  if (blob != null) {
    return converter(blob);
  } else {
    return converter;
  }
}
function der(it){
  if (isBuffer(it)) {
    return it;
  } else {
    return bufferFrom(it, 'binary');
  }
}
function pem(it){
  var lines, i$, to$, i;
  it = der(it).toString('base64');
  lines = ['-----BEGIN CERTIFICATE-----'];
  for (i$ = 0, to$ = it.length; i$ < to$; i$ += 64) {
    i = i$;
    lines.push(it.substr(i, 64));
  }
  lines.push('-----END CERTIFICATE-----', '');
  return lines.join("\r\n");
}
function txt(it){
  var crt;
  crt = asn1(it);
  return "Subject\t" + crt.subject.value.map(function(it){
    return bufferFrom(it.value[0].value[1].value, 'binary').toString('utf8');
  }).join('/') + "\nValid\t" + crt.valid.value.map(function(it){
    return it.value;
  }).join(' - ') + "\n" + pem(it);
}
function asn1(it){
  var asn1parser, crt, serial, hasSerial;
  asn1parser = forge$().asn1;
  it = it.toString('binary');
  crt = asn1parser.fromDer(it).value[0].value;
  serial = crt[0];
  hasSerial = serial.tagClass === asn1parser.Class.CONTEXT_SPECIFIC && serial.type === 0 && serial.constructed;
  crt = crt.slice(hasSerial);
  return {
    serial: crt[0],
    valid: crt[3],
    issuer: crt[2],
    subject: crt[4]
  };
}
function x509(it){
  return forge$().pki.certificateFromAsn1(
  forge$().asn1.fromDer(
  it.toString('binary')));
}