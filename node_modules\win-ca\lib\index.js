// Generated by LiveScript 1.6.0
var disabled, nApi, der2;
module.exports = api;
api.disabled = disabled = process.platform !== 'win32';
api.nApi = nApi = !!process.versions.napi && api === require('../fallback') && !(api.electron = require('is-electron')());
api.der2 = der2 = require('./der2');
api.hash = hash;
api.inject = inject;
api.exe = exe;
importAll$(api, require('./v2'));
if (!disabled && api === require('../api')) {
  api({
    inject: true,
    $ave: true,
    async: true
  });
}
function hash(){
  return (api.hash = require('./hash')).apply(this, arguments);
}
function inject(){
  return (api.inject = require('./inject').inject).apply(this, arguments);
}
function exe(){
  return (api.exe = require('./fallback').exe).apply(this, arguments);
}
function api(params){
  var engine, ref$, store, async, Process, unique, mapper, saver, injector;
  params == null && (params = {});
  engine = disabled || params.disabled
    ? require('./none')
    : ((ref$ = params.fallback) != null
      ? ref$
      : !nApi)
      ? require('./fallback')
      : require('./n-api');
  if (!(store = params.store)) {
    store = [];
  } else if (!Array.isArray(store)) {
    store = [store];
  }
  engine = engine[(async = params.async) ? 'async' : 'sync'](store);
  Process = async ? asyncProcess : syncProcess;
  if (false !== params.unique) {
    unique = require('./unique')();
  }
  mapper = der2(params.format);
  if (Array.isArray(params.ondata)) {
    params.ondata = params.ondata.push.bind(params.ondata);
  }
  if (params.save || params.$ave) {
    saver = require('./save')(params);
  }
  if (params.inject) {
    injector = require('./inject')(params.inject);
  }
  if (params.generator) {
    return (async ? asyncGenerator : syncGenerator)();
  }
  engine.run(function(it){
    if (!it || !unique || unique(it)) {
      Process(it);
    }
  });
  function syncProcess(it){
    if (saver) {
      saver(it);
    }
    if (it) {
      if (injector) {
        injector(it);
      }
      if (typeof params.ondata == 'function') {
        params.ondata(mapper(it));
      }
    } else {
      if (typeof params.onend == 'function') {
        params.onend();
      }
    }
  }
  function asyncProcess(it){
    Promise.resolve(it).then(syncProcess);
  }
  function syncGenerator(){
    var ref$;
    return ref$ = {}, ref$[Symbol.iterator] = myself, ref$['return'] = Return, ref$.next = syncNext, ref$;
  }
  function asyncGenerator(){
    var ref$, ref1$;
    return ref$ = {}, ref$[(ref1$ = Symbol.asyncIterator) != null ? ref1$ : '@'] = myself, ref$['return'] = Return, ref$.next = asyncNext, ref$;
  }
  function genProcess(it){
    Process(it);
    return {
      done: !it,
      value: it != null ? mapper(it) : it
    };
  }
  function Return(it){
    engine.done();
    return {
      done: true,
      value: it
    };
  }
  function syncNext(){
    var der;
    while ((der = engine.next()) && unique && !unique(der)) {}
    return genProcess(der);
  }
  function asyncNext(){
    function fire(){
      return Promise.resolve().then(engine.next).then(function(it){
        if (it && unique && !unique(it)) {
          return fire();
        } else {
          return genProcess(it);
        }
      });
    }
    return fire();
  }
}
function myself(){
  return this;
}
function importAll$(obj, src){
  for (var key in src) obj[key] = src[key];
  return obj;
}