{"name": "kiroAgent", "displayName": "kiroAgent", "publisher": "kiro", "description": "GenAI assisted development experience", "version": "0.1.0", "private": true, "capabilities": {"untrustedWorkspaces": {"supported": true, "description": "<PERSON><PERSON> is for everywhere"}}, "license": "AWS-IPL", "repository": "https://github.com/kiro-team/kiro-extension.git", "homepage": "https://github.com/kiro-team/kiro-extension", "engines": {"vscode": "^1.94.0", "node": "^20.18.1"}, "workspaces": ["packages/@amzn/codewhisperer-runtime", "packages/hook-editor", "packages/webview-components", "packages/kiricons", "packages/kiro-shared", "packages/kiro-shared-types", "packages/continuedev/config-types", "packages/continuedev/core", "packages/continuedev/extension", "packages/continuedev/gui", "packages/continuedev/llm-info"], "categories": ["Other"], "activationEvents": ["onFileSystem:kiro-diff", "onFileSystem:kiro-meta", "onFileSystem:kiro-spec", "onStartupFinished", "onWebviewPanel:views.onboarding", "onView:continueGUIView", "onView:kiroAgent.hooksView", "onLanguage:javascript", "onLanguage:typescript", "onLanguage:javascriptreact", "onLanguage:typescriptreact", "onCommand:kiroAgent.onboarding.checkSteps", "onCommand:kiroAgent.onboarding.checkStep", "onCommand:kiroAgent.onboarding.executeStep", "onCommand:kiroAgent.onboarding.executeStep", "onCommand:kiroAgent.configuration.startOnboarding", "onCommand:kiroAgent.configuration.completeOnboarding", "onCommand:kiroAgent.viewHome", "onCommand:kiroAgent.viewLetsBuild", "onCommand:kiroAgent.executions.getExecutionHistory"], "enabledApiProposals": ["findTextInFiles", "editorInlineActions", "notebookCellGroup", "contribWebviewProvider", "contribSignInController", "chatVariableResolver", "chatParticipantPrivate", "chatParticipantAdditions", "defaultChatParticipant", "quickDiffProvider", "contribDiffEditorGutterToolBarMenus", "diffCommand", "terminalExecuteCommandEvent", "scmMultiDiffEditor", "styledCodeLens", "contribSpecEditorToolbarMenus", "contribSteeringToolbarMenus", "contribMcpConfigToolbarMenus", "findTextInFilesNew", "textSearchProviderNew", "nativeNotification", "terminalDataWriteEvent"], "main": "./dist/extension.js", "l10n": "./l10n", "contributes": {"authentication": [{"id": "kiro", "label": "<PERSON><PERSON>"}], "languages": [{"filenames": ["config.json", ".continuerc.json"], "id": "jsonc"}], "semanticTokenTypes": [{"id": "contextProvider", "superType": "namespace", "description": "Context provider type"}, {"id": "pathSegment", "superType": "parameter", "description": "Path segment in context provider"}, {"id": "delimiter", "superType": "operator", "description": "Delimiter in context provider syntax"}], "semanticTokenScopes": [{"language": "markdown", "scopes": {"contextProvider": ["entity.name.class.context-provider"], "pathSegment": ["variable.parameter.context-provider"], "delimiter": ["punctuation.separator.context-provider"]}}], "configurationDefaults": {"[markdown]": {"editor.semanticHighlighting.enabled": true}}, "chatParticipants": [{"id": "kiroAgent.chat", "fullName": "%KiroAgent.title%", "name": "<PERSON><PERSON>", "description": "%KiroAgent.command.edit.instructions%", "isSticky": false, "isDefault": true, "locations": ["panel", "editor", "notebook"], "commands": [], "disambiguation": []}], "commands": [{"command": "kiroAgent.inlineChat.start", "title": "%KiroAgent.command.inlineChat.start%", "category": "%KiroAgent.title%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.acceptDiff", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.acceptDiff%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.rejectDiff", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.rejectDiff%", "group": "%KiroAgent.title%", "icon": "$(stop)"}, {"command": "kiroAgent.acceptVerticalDiffBlock", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.acceptVerticalDiffBlock%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.rejectVerticalDiffBlock", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.rejectVerticalDiffBlock%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.quickEdit", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.quickEdit%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.focusContinueInput", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.focusContinueInput%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.focusContinueInputWithoutClear", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.focusContinueInput%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.debugTerminal", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.debugTerminal%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.toggleTabAutocompleteEnabled", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.toggleTabAutocompleteEnabled%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.selectFilesAsContext", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.selectFilesAsContext%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.newSession", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.newSession%", "icon": "$(add)", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.viewHistoryChats", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.viewHistory%", "icon": "$(history)", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.writeCommentsForCode", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.writeCommentsForCode%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.writeDocstringForCode", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.writeDocstringForCode%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.fixCode", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.fixCode%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.optimizeCode", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.optimizeCode%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.fixGrammar", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.fixGrammar%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.codebaseForceReIndex", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.codebaseForceReIndex%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.rebuildCodebaseIndex", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.rebuildCodebaseIndex%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.docsIndex", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.docsIndex%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.docsReIndex", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.docsReIndex%", "group": "%KiroAgent.title%"}, {"command": "kiroAgent.openWorkspaceMcpConfig", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.openWorkspaceMcpConfig%", "group": "%KiroAgent.title%", "shortTitle": "%KiroAgent.command.openWorkspaceMcpConfig.short%"}, {"command": "kiroAgent.openUserMcpConfig", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.openUserMcpConfig%", "group": "%KiroAgent.title%", "shortTitle": "%KiroAgent.command.openUserMcpConfig.short%"}, {"command": "kiroAgent.openActiveMcpConfig", "category": "%KiroAgent.title%", "title": "%KiroAgent.command.openActiveMcpConfig%", "icon": "$(pencil)"}, {"command": "kiroAgent.debug.SetQOnboardingState", "title": "%KiroAgent.debug.setQOnboardingState%", "category": "%KiroAgent.debugTitle%"}, {"command": "kiroAgent.deleteAccount", "title": "Delete Account", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.agent.askAgent", "title": "%KiroAgent.agent.askAgent%", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.agent.rewriteRange", "title": "%KiroAgent.agent.rewriteRange%", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.debug.specfs", "title": "%KiroAgent.debug.openTestNotebook%", "category": "%KiroAgent.debugTitle%"}, {"command": "kiroAgent.agent.promptAgent", "title": "%KiroAgent.agent.promptAgent%", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.agent.clearOutput", "title": "%KiroAgent.clearOutput%", "category": "%KiroAgent.title%", "icon": "$(clear-all)"}, {"command": "kiroAgent.agent.cancelRunningAgent", "title": "%KiroAgent.cancelRunningAgent%", "icon": "$(close)", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.createDebugLogZip", "title": "Create Debug Log Zip", "category": "%KiroAgent.title%", "icon": "$(code-oss)"}, {"command": "kiroAgent.debug.captureLog", "title": "%KiroAgent.command.captureLog%", "category": "%KiroAgent.debugTitle%", "icon": "$(code-oss)"}, {"command": "kiroAgent.debug.captureLLMLog", "title": "%KiroAgent.command.captureLLMLog%", "category": "%KiroAgent.debugTitle%", "icon": "$(code-oss)"}, {"command": "kiroAgent.debug.resetOnboardingState", "title": "%KiroAgent.debug.resetOnboardingState%", "category": "%KiroAgent.debugTitle%", "icon": "$(code-oss)"}, {"command": "kiroAgent.hooks.openUI", "title": "%KiroAgent.hooks.openUI%", "category": "%KiroAgent.title%", "icon": "$(plus)"}, {"command": "kiroAgent.debug.openMetadata", "title": "%KiroAgent.debug.openMetadata%", "category": "%KiroAgent.debugTitle%"}, {"command": "kiroAgent.recordReferences", "title": "%KiroAgent.command.recordReferences%", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.debug.purgeMetadata", "title": "%KiroAgent.debug.purgeMetadata%", "category": "%KiroAgent.debugTitle%"}, {"command": "kiroAgent.checkpoint.acceptDiff", "title": "%KiroAgent.checkpoints.acceptDiff%", "category": "%KiroAgent.debugTitle%", "icon": "$(check)", "when": "diffEditorModifiedUri =~ /^kiro-meta\\:.*/"}, {"command": "kiroAgent.executions.uiControl", "title": "%KiroAgent.executions.uiControl%", "category": "%KiroAgent.debugTitle%"}, {"command": "kiro.steering.createInitialSteering", "category": "%KiroAgent.title%", "title": "%Kiro.steering.createInitialSteering%"}, {"command": "kiro.steering.createSteering", "category": "%KiroAgent.title%", "title": "%Kiro.steering.createSteering%", "icon": "$(plus)"}, {"command": "kiro.steering.deleteSteering", "title": "Delete Steering", "category": "%KiroAgent.title%", "icon": "$(trash)"}, {"command": "kiro.steering.refineSteeringFile", "title": "%Kiro.steering.refineSteeringFile%", "shortTitle": "Refine", "category": "%KiroAgent.title%"}, {"command": "kiro.spec.previousDocument", "title": "Previous document", "icon": "$(arrow-left)"}, {"command": "kiro.spec.nextDocument", "title": "Next document", "icon": "$(arrow-right)"}, {"command": "kiro.spec.navigateToRequirements", "title": "%Kiro.spec.navigateToRequirements.long%", "shortTitle": "%Kiro.spec.navigateToRequirements%", "enablement": "!(resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]requirements\\.md$/)"}, {"command": "kiro.spec.navigateToDesign", "title": "%Kiro.spec.navigateToDesign.long%", "shortTitle": "%Kiro.spec.navigateToDesign%", "enablement": "!(resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]design\\.md$/) && !(kiro.spec.isResourceEmpty && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]requirements\\.md$/)"}, {"command": "kiro.spec.navigateToTasks", "title": "%Kiro.spec.navigateToTasks.long%", "shortTitle": "%Kiro.spec.navigateToTasks%", "enablement": "!(resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]tasks\\.md$/) && !(kiro.spec.isResourceEmpty && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/](requirements|design)\\.md$/)"}, {"command": "kiro.spec.refreshRequirementsFile", "title": "%Kiro.spec.refreshRequirementsFile.long%", "shortTitle": "%Kiro.spec.refreshRequirementsFile%", "enablement": "!kiro.spec.isResourceEmpty || activeEditorIsDirty"}, {"command": "kiro.spec.refreshDesignFile", "title": "%Kiro.spec.refreshDesignFile.long%", "shortTitle": "%Kiro.spec.refreshDesignFile%"}, {"command": "kiro.spec.refreshPlanFile", "title": "%Kiro.spec.refreshPlanFile.long%", "shortTitle": "%Kiro.spec.refreshPlanFile%"}, {"command": "kiro.spec.explorerCreateSpec", "title": "%Kiro.spec.explorerCreateSpec%", "category": "%KiroAgent.title%", "icon": "$(plus)"}, {"command": "kiro.spec.explorerDeleteSpec", "title": "Delete Spec", "category": "%KiroAgent.title%", "icon": "$(trash)"}, {"command": "kiroAgent.enableShellIntegration", "title": "%KiroAgent.command.enableShellIntegration%", "category": "%KiroAgent.title%"}, {"command": "kiroAgent.mcp.showLogs", "title": "Show MCP Logs", "icon": "$(info)"}, {"command": "kiroAgent.mcp.resetConnection", "title": "Reconnect", "icon": "$(refresh)"}], "keybindings": [{"command": "kiroAgent.focusContinueInput", "mac": "cmd+l", "key": "ctrl+l"}, {"command": "kiroAgent.focusContinueInputWithoutClear", "mac": "cmd+shift+l", "key": "ctrl+shift+l"}, {"command": "kiroAgent.acceptDiff", "mac": "shift+cmd+enter", "key": "shift+ctrl+enter"}, {"command": "kiroAgent.rejectDiff", "mac": "shift+cmd+backspace", "key": "shift+ctrl+backspace"}, {"command": "kiroAgent.rejectDiff", "mac": "cmd+z", "key": "ctrl+z", "when": "kiroAgent.diffVisible"}, {"command": "kiroAgent.quickEditHistoryUp", "mac": "up", "key": "up", "when": "false && continue.quickEditHistoryFocused"}, {"command": "kiroAgent.quickEditHistoryDown", "mac": "down", "key": "down", "when": "false && continue.quickEditHistoryFocused"}, {"command": "kiroAgent.acceptVerticalDiffBlock", "mac": "alt+cmd+y", "key": "alt+ctrl+y"}, {"command": "kiroAgent.rejectVerticalDiffBlock", "mac": "alt+cmd+n", "key": "alt+ctrl+n"}, {"command": "kiroAgent.inlineChat.start", "mac": "cmd+i", "key": "ctrl+i"}, {"command": "kiroAgent.debugTerminal", "mac": "cmd+shift+r", "key": "ctrl+shift+r"}, {"command": "kiroAgent.toggleTabAutocompleteEnabled", "mac": "cmd+k cmd+a", "key": "ctrl+k ctrl+a", "when": "!terminalFocus"}, {"command": "kiro.spec.refreshRequirementsFile", "key": "ctrl+shift+enter", "mac": "cmd+shift+enter", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]requirements\\.md$/"}, {"command": "kiro.spec.refreshDesignFile", "key": "ctrl+shift+enter", "mac": "cmd+shift+enter", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]design\\.md$/"}, {"command": "kiro.spec.refreshPlanFile", "key": "ctrl+shift+enter", "mac": "cmd+shift+enter", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]tasks\\.md$/"}], "submenus": [{"id": "kiroAgent.continueSubMenu", "label": "%KiroAgent.title%"}], "menus": {"commandPalette": [{"command": "kiroAgent.quickEdit"}, {"command": "kiroAgent.focusContinueInput"}, {"command": "kiroAgent.focusContinueInputWithoutClear"}, {"command": "kiroAgent.debugTerminal"}, {"command": "kiroAgent.newSession"}, {"command": "kiroAgent.openWorkspaceMcpConfig"}, {"command": "kiroAgent.openUserMcpConfig"}, {"command": "kiroAgent.debug.SetQOnboardingState", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.debug.specfs", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.debug.captureLog", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.debug.captureLLMLog", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.debug.resetOnboardingState", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.debug.openMetadata", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.debug.purgeMetadata", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiroAgent.executions.uiControl", "when": "kiroAgent.settings.enableDevMode"}, {"command": "kiro.spec.refreshRequirementsFile", "when": "false"}, {"command": "kiro.spec.refreshDesignFile", "when": "false"}, {"command": "kiro.spec.refreshPlanFile", "when": "false"}, {"command": "kiro.spec.nextDocument", "when": "false"}, {"command": "kiro.spec.previousDocument", "when": "false"}, {"command": "kiro.steering.deleteSteering", "when": "false"}], "editor/context": [{"submenu": "kiroAgent.continueSubMenu", "group": "0_acontinue"}], "editor/inlineActions": [{"command": "kiroAgent.focusContinueInput"}, {"command": "kiroAgent.inlineChat.start"}], "editor/title/run": [{"command": "kiroAgent.rejectDiff", "group": "%KiroAgent.title%", "when": "kiroAgent.streamingDiff"}], "kiroAgent.continueSubMenu": [{"command": "kiroAgent.focusContinueInputWithoutClear", "group": "%KiroAgent.title%", "when": "editorHasSelection"}, {"command": "kiroAgent.writeCommentsForCode", "group": "%KiroAgent.title%", "when": "editorHasSelection"}, {"command": "kiroAgent.writeDocstringForCode", "group": "%KiroAgent.title%", "when": "editorHasSelection"}, {"command": "kiroAgent.fixCode", "group": "%KiroAgent.title%", "when": "editorHasSelection"}, {"command": "kiroAgent.optimizeCode", "group": "%KiroAgent.title%", "when": "editorHasSelection"}, {"command": "kiroAgent.fixGrammar", "group": "%KiroAgent.title%", "when": "editorHasSelection && editorLangId == 'markdown'"}], "explorer/context": [{"command": "kiroAgent.selectFilesAsContext", "group": "1_debug@1"}], "view/title": [{"command": "kiroAgent.viewHistoryChats", "group": "navigation@1", "when": "view == kiroAgent.continueGUIView"}, {"command": "kiroAgent.hooks.openUI", "when": "view == kiroAgent.views.hooksStatus", "group": "navigation"}, {"command": "kiro.spec.explorerCreateSpec", "when": "view == kiro.views.specExplorer", "group": "navigation"}, {"command": "kiro.steering.createSteering", "when": "view == kiroAgent.views.steeringExplorer", "group": "navigation"}, {"command": "kiroAgent.openActiveMcpConfig", "when": "view == kiroAgent.views.mcpServerStatus", "group": "navigation"}], "view/item/context": [{"command": "kiro.spec.explorerDeleteSpec", "when": "view == kiro.views.specExplorer"}, {"command": "kiro.steering.deleteSteering", "when": "view == kiroAgent.views.steeringExplorer"}, {"command": "kiroAgent.mcp.showLogs", "when": "viewItem == mcpShowLogs", "group": "inline"}, {"command": "kiroAgent.mcp.resetConnection", "when": "viewItem == mcpResetConnection", "group": "inline"}], "terminal/context": [{"command": "kiroAgent.debugTerminal", "group": "navigation@top"}], "spec/editor/navbar": [{"command": "kiro.spec.navigateToRequirements", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/](design|requirements|tasks)\\.md$/", "group": "navigation@1"}, {"command": "kiro.spec.navigateToDesign", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/](design|requirements|tasks)\\.md$/", "group": "navigation@2"}, {"command": "kiro.spec.navigateToTasks", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/](design|requirements|tasks)\\.md$/", "group": "navigation@3"}], "steering/toolbar": [{"command": "kiro.steering.refineSteeringFile", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]steering[\\\\/].*\\.md$/", "group": "actions@1"}], "spec/editor/toolbar": [{"command": "kiro.spec.refreshRequirementsFile", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]requirements\\.md$/", "group": "actions@1"}, {"command": "kiro.spec.refreshDesignFile", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]design\\.md$/", "group": "actions@1"}, {"command": "kiro.spec.refreshPlanFile", "when": "!isInDiffEditor && resourcePath =~ /\\.kiro[\\\\/]specs[\\\\/].*[\\\\/]tasks\\.md$/", "group": "actions@1"}], "mcp/config/toolbar": [{"command": "kiroAgent.openWorkspaceMcpConfig", "when": "resourcePath =~ /\\.kiro[\\\\/]settings[\\\\/]mcp\\.json$/", "group": "actions@1"}, {"command": "kiroAgent.openUserMcpConfig", "when": "resourcePath =~ /\\.kiro[\\\\/]settings[\\\\/]mcp\\.json$/", "group": "actions@1"}], "diffEditor/gutter/hunk": [{"command": "kiroAgent.checkpoint.acceptDiff", "group": "primary", "when": "diffEditorModifiedUri =~ /^kiro-meta\\:.*/"}]}, "customEditors": [{"viewType": "testSpecification.editor", "displayName": "%KiroAgent.testSpecificationEditor.title%", "selector": [{"filenamePattern": "*"}], "priority": "option"}, {"viewType": "chatContext.chatViewer", "displayName": "%KiroAgent.chatContext.title%", "selector": [{"filenamePattern": "*.chat"}]}], "viewsContainers": {"activitybar": [{"id": "continue", "title": "Cha<PERSON>", "icon": "./packages/continuedev/extension/media/sidebar-icon.png"}, {"id": "kiro", "title": "<PERSON><PERSON>", "icon": "./packages/kiricons/src/kiro.svg"}]}, "views": {"kiro": [{"id": "kiro.views.emptyWorkspace", "name": "", "when": "workbenchState == empty || workspaceFolderCount == 0"}, {"id": "kiro.views.specExplorer", "name": "Specs", "when": "!(workbenchState == empty || workspaceFolderCount == 0)"}, {"id": "kiroAgent.views.hooksStatus", "name": "%KiroAgent.views.hooks%", "when": "!(workbenchState == empty || workspaceFolderCount == 0)"}, {"id": "kiroAgent.views.steeringExplorer", "name": "%KiroAgent.views.steering%", "when": "!(workbenchState == empty || workspaceFolderCount == 0)"}, {"id": "kiroAgent.views.mcpServerStatus", "name": "%KiroAgent.views.mcpServerStatus%", "when": "!(workbenchState == empty || workspaceFolderCount == 0)"}], "continue": [{"type": "webview", "id": "kiroAgent.continueGUIView", "name": "", "visibility": "visible"}]}, "viewsWelcome": [{"view": "kiro.views.emptyWorkspace", "when": "workbenchState == empty", "contents": "Open a project to start working with <PERSON><PERSON>\n[Open Project](command:workbench.action.files.openFolder)"}, {"view": "kiro.views.emptyWorkspace", "when": "workbenchState == workspace && workspaceFolderCount == 0", "contents": "Open a project to start working with <PERSON><PERSON>\n[Open Project](command:workbench.action.files.openFolderViaWorkspace)"}, {"view": "kiro.views.specExplorer", "contents": "Build complex features with structured planning"}, {"view": "kiroAgent.views.steeringExplorer", "contents": "Guide agent behavior and responses \n [Generate Steering Docs](command:kiro.steering.createInitialSteering)"}, {"view": "kiroAgent.views.hooksStatus", "contents": "Automate repetitive tasks with smart triggers"}, {"view": "kiroAgent.views.mcpServerStatus", "when": "kiroAgent.settings.configureMCP != Disabled", "contents": "%KiroAgent.treeview.welcome.mcp%"}, {"view": "kiroAgent.views.mcpServerStatus", "when": "kiroAgent.settings.configureMCP == Disabled", "contents": "%KiroAgent.treeview.welcome.mcp.disabled%"}], "jsonValidation": [{"fileMatch": ".hooks/*.json", "url": "./extension-resources/hook.json"}, {"fileMatch": "config.json", "url": "./packages/continuedev/extension/config_schema.json"}, {"fileMatch": ".continuerc.json", "url": "./packages/continuedev/extension/continue_rc_schema.json"}]}, "lint-staged": {".spellcheck-dictionary.json": "node scripts/sort-spellcheck-dictionary.mjs && prettier --write"}, "kiroAgent": {"testConfig": {"script": "kiroAgent:test", "testReportPath": "test-results.xml", "testDirectoryType": "immediateParentDirectory", "testDirectoryName": "__tests__", "framework": "mocha"}}}