// Generated by LiveScript 1.6.0
var path, child_process, split, bin, execFileProperties, bufferFrom, out$ = typeof exports != 'undefined' && exports || this;
path = require('path');
child_process = require('child_process');
split = require('split');
execFileProperties = {
  maxBuffer: 1024 * 1024 * 1024
};
function exe(newBin){
  var old;
  newBin == null && (newBin = 'roots.exe');
  old = bin;
  bin = path.resolve(__dirname, newBin);
  return old;
} exe();
out$.exe = exe;
out$.sync = sync;
function sync(args){
  var queue, index;
  return {
    run: run,
    next: next,
    done: done
  };
  function next(){
    if (!queue) {
      queue = [];
      index = 0;
      run(function(it){
        if (it) {
          queue.push(it);
        }
      });
    }
    if (index < queue.length) {
      return queue[index++];
    }
    queue = [];
  }
  function done(){
    queue = [];
  }
  function run(callback){
    splitter(callback).on('end', function(){
      callback();
    }).end(child_process.execFileSync(bin, args, execFileProperties));
  }
}
out$.async = async;
function async(args){
  var queue, requests, finished;
  return {
    run: run,
    next: next,
    done: done
  };
  function next(){
    if (!queue) {
      queue = [];
      requests = [];
      run(enqueue);
    }
    return queue.length
      ? Promise.resolve(queue.shift())
      : finished
        ? Promise.resolve()
        : new Promise(function(it){
          requests.push(it);
        });
  }
  function enqueue(it){
    if (finished) {
      return;
    }
    if (!it) {
      suspend();
    } else if (requests.length) {
      requests.shift()(it);
    } else {
      queue.push(it);
    }
  }
  function done(){
    queue = [];
    suspend();
  }
  function suspend(){
    var i$, ref$, len$, resolver;
    finished = true;
    for (i$ = 0, len$ = (ref$ = requests).length; i$ < len$; ++i$) {
      resolver = ref$[i$];
      resolver();
    }
  }
  function run(callback){
    child_process.execFile(bin, args, execFileProperties, function(){}).stdout.pipe(splitter(callback)).on('end', function(){
      callback();
    });
  }
}
function splitter(callback){
  return split(function(line){
    if (line.length) {
      callback(bufferFrom(line, 'hex'));
    }
  });
}
bufferFrom = Buffer.from || function(data, encoding){
  return new Buffer(data, encoding);
};