import { InferenceSession as InferenceSessionInterface } from './inference-session';
type SessionOptions = InferenceSessionInterface.SessionOptions;
type RunOptions = InferenceSessionInterface.RunOptions;
type FeedsType = InferenceSessionInterface.FeedsType;
type FetchesType = InferenceSessionInterface.FetchesType;
type ReturnType = InferenceSessionInterface.ReturnType;
export declare class InferenceSession implements InferenceSessionInterface {
    private constructor();
    run(feeds: FeedsType, options?: RunOptions): Promise<ReturnType>;
    run(feeds: FeedsType, fetches: FetchesType, options?: RunOptions): Promise<ReturnType>;
    static create(path: string, options?: SessionOptions): Promise<InferenceSessionInterface>;
    static create(buffer: Array<PERSON>ufferLike, options?: SessionOptions): Promise<InferenceSessionInterface>;
    static create(buffer: Array<PERSON>uffer<PERSON>ike, byteOffset: number, byteLength?: number, options?: SessionOptions): Promise<InferenceSessionInterface>;
    static create(buffer: Uint8Array, options?: SessionOptions): Promise<InferenceSessionInterface>;
    startProfiling(): void;
    endProfiling(): void;
    get inputNames(): readonly string[];
    get outputNames(): readonly string[];
    private handler;
}
export {};
