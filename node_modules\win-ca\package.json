{"name": "win-ca", "version": "3.5.1", "description": "Get Windows System Root certificates", "keywords": ["n-api", "ssl", "tls", "ca", "root", "windows", "vscode", "electron"], "homepage": "https://github.com/ukoloff/win-ca", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ukoloff/win-ca.git"}, "bugs": {"url": "https://github.com/ukoloff/win-ca/issues"}, "main": "lib", "scripts": {"nvm$": "n-api\\nvm$deploy", "pretest": "lsc -cbo lib src", "postinstall": "node -e \"try{require('.')}catch(e){}\"", "test": "mocha"}, "mocha": {"extension": ["ls", "js"], "reporter": "dot", "require": ["livescript", "choma", "appveyor-mocha"]}, "files": ["lib", "api", "fallback"], "dependencies": {"is-electron": "^2.2.0", "make-dir": "^1.3.0", "node-forge": "^1.2.1", "split": "^1.0.1"}, "devDependencies": {"appveyor-mocha": "^1.1.2", "bindings": "^1.5.0", "choma": "^1.2.1", "fs-extra": "^5.0.0", "livescript": "^1.6.0", "mocha": "^6.2.3", "node-addon-api": "^3.0.0"}}