// Generated by LiveScript 1.6.0
exports.all = all;
exports.each = each;
each.async = async;
function each(){
  var params;
  upgradeAPI(arguments, params = {
    unique: true,
    ondata: function(it){
      if (typeof params.$cb == 'function') {
        params.$cb(it);
      }
    }
  });
}
function async(){
  var params;
  upgradeAPI(arguments, params = {
    async: true,
    ondata: function(it){
      if (typeof params.$cb == 'function') {
        params.$cb(void 8, it);
      }
    },
    onend: function(){
      if (typeof params.$cb == 'function') {
        params.$cb();
      }
    }
  });
}
function all(){
  var result;
  upgradeAPI(arguments, {
    ondata: result = []
  });
  return result;
}
function upgradeAPI(args, defaults){
  var api, format;
  api = require('.');
  format = args[0];
  defaults.unique == null && (defaults.unique = false);
  defaults.format = format != null
    ? format
    : api.der2.x509;
  defaults.$cb = args[1] || format;
  api(defaults);
}